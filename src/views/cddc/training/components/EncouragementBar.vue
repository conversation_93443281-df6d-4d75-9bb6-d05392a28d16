<template>
  <div class="encouragement-bar">
    <ScreenTitle>鼓励加油</ScreenTitle>
    <ScreenBox class="encouragement-content">
      <div class="encouragement-message">
        <div class="message-icon">
          <SvgIcon name="g-icon-like" />
        </div>
        <div class="message-text">
          <div class="main-message">{{ currentMessage.text }}</div>
          <div class="sub-message">{{ currentMessage.subText }}</div>
        </div>
      </div>
      
      <div class="progress-indicator">
        <div class="progress-circle">
          <div class="circle-progress" :style="{ '--progress': progressPercentage + '%' }">
            <span class="progress-text">{{ progressPercentage }}%</span>
          </div>
        </div>
        <div class="progress-label">训练完成度</div>
      </div>
    </ScreenBox>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import { SvgIcon } from '@geega-ui-plus/geega-ui';

const props = defineProps<{
  trainProgress?: {
    trainFrequency: number;
    requestFrequency: number;
    trainDuration: number;
    requestDuration: number;
  };
  realTimeData?: {
    totalActionRate: number;
    totalQualificationRate: number;
  };
}>();

// 鼓励消息列表
const encouragementMessages = [
  { text: '加油！你做得很棒！', subText: '继续保持这个节奏' },
  { text: '很好！动作很标准！', subText: '专注度很高，继续努力' },
  { text: '优秀！进步明显！', subText: '你的技能在不断提升' },
  { text: '棒极了！', subText: '保持专注，再接再厉' },
  { text: '表现出色！', subText: '你的努力正在得到回报' },
];

// 当前显示的消息
const currentMessageIndex = ref(0);
const currentMessage = computed(() => encouragementMessages[currentMessageIndex.value]);

// 训练完成度百分比
const progressPercentage = computed(() => {
  const progress = props.trainProgress;
  if (!progress || !progress.requestFrequency) return 0;
  
  return Math.min(100, Math.floor((progress.trainFrequency / progress.requestFrequency) * 100));
});

// 根据训练数据变化切换鼓励消息
watch(
  () => props.realTimeData?.totalActionRate,
  (newRate, oldRate) => {
    if (newRate && newRate > (oldRate || 0)) {
      // 动作达标率提升时切换消息
      currentMessageIndex.value = (currentMessageIndex.value + 1) % encouragementMessages.length;
    }
  }
);

// 定时切换消息（每30秒）
setInterval(() => {
  currentMessageIndex.value = (currentMessageIndex.value + 1) % encouragementMessages.length;
}, 30000);
</script>

<style lang="less" scoped>
.encouragement-bar {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;

  .encouragement-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.2vw;
  }

  .encouragement-message {
    display: flex;
    align-items: center;
    gap: 0.8vw;

    .message-icon {
      font-size: 2.4vw;
      color: #29e8ab;
      flex-shrink: 0;
    }

    .message-text {
      flex: 1;

      .main-message {
        font-size: 1.1vw;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.3vh;
        line-height: 1.4;
      }

      .sub-message {
        font-size: 0.9vw;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.3;
      }
    }
  }

  .progress-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.6vh;

    .progress-circle {
      position: relative;
      width: 4vw;
      height: 4vw;

      .circle-progress {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
          #29e8ab 0deg,
          #29e8ab calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.1) calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.1) 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          width: 3.2vw;
          height: 3.2vw;
          background: #040405;
          border-radius: 50%;
        }

        .progress-text {
          position: relative;
          z-index: 1;
          font-size: 0.8vw;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }

    .progress-label {
      font-size: 0.8vw;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
    }
  }
}

// 响应式适配
@media screen and (max-width: 1024px) {
  .encouragement-bar {
    gap: 0.6vw;

    .encouragement-content {
      padding: 1vw;
    }

    .encouragement-message {
      gap: 0.6vw;

      .message-icon {
        font-size: 2vw;
      }

      .message-text {
        .main-message {
          font-size: 0.9vw;
        }

        .sub-message {
          font-size: 0.7vw;
        }
      }
    }

    .progress-indicator {
      .progress-circle {
        width: 3.5vw;
        height: 3.5vw;

        .circle-progress {
          &::before {
            width: 2.8vw;
            height: 2.8vw;
          }

          .progress-text {
            font-size: 0.7vw;
          }
        }
      }

      .progress-label {
        font-size: 0.7vw;
      }
    }
  }
}
</style>
